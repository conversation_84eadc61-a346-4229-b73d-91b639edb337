open Vitest
open TestingLibraryReact

let userEvent = TestingLibraryEvent.setup()

module TestableInputSuggestionComboBoxField = {
  @react.component
  let make = (~disabled=?, ~onChange=fn1(ignore)->fn, ~onFocus=fn0()->fn, ~onBlur=fn0()->fn) => {
    let (value, setValue) = React.useState(() => "")
    let (focused, setFocused) = React.useState(() => false)

    <InputSuggestionComboBoxField
      label="My Label"
      ?disabled
      focused
      value
      items={[{value: "My first key"}, {value: "My second key"}]}
      onChange={value => {
        onChange(value)
        setValue(_ => value)
      }}
      onFocus={() => {
        onFocus()
        setFocused(_ => true)
      }}
      onBlur={() => {
        onBlur()
        setFocused(_ => false)
      }}
    />
  }
}

let expectListBoxIsOpened = () => {
  let inputElement = screen->getByRoleWithOptionsExn(#combobox, {expanded: true, hidden: true})

  expect(inputElement)->toBeVisible
  expect(screen->getByRoleExn(#listbox))->toBeVisible
}

let expectListBoxIsClosed = () => {
  let inputElement = screen->getByRoleWithOptionsExn(#combobox, {expanded: false, hidden: false})

  expect(inputElement)->toBeVisible
  expect(screen->queryByRole(#listbox))->toBeNone
}

type testDialogOption = {
  value: string,
  selected: bool,
}
let expectListBoxOptionsToStrictEqual = results => {
  let options = screen->getAllByRoleExn(#option)

  expect(options)->toHaveLength(results->Array.length)
  expect(
    options->Array.map(option =>
      option->WebAPI.DomElement.asHtmlOptionElement->WebAPI.HtmlOptionElement.textContent
    ),
  )->toStrictEqual(results->Array.map(option => option.value))

  options->Array.forEachWithIndex((index, option) => {
    expect(option)->toHaveAttributeValue(
      "aria-selected",
      (results->Array.getExn(index)).selected->Js.String2.make,
    )
  })
}

let clickListboxOption = async index => {
  let options = screen->getAllByRoleExn(#option)

  await userEvent->TestingLibraryEvent.click(options->Array.getExn(index))
}

type testFieldChange = {
  value: string,
  calledTimes: int,
}
let expectFieldChange = (onChange, result) => {
  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toHaveValue(result.value)
  expect(onChange)->toHaveBeenLastCalledWith1(result.value)
  expect(onChange)->toHaveBeenCalledTimes(result.calledTimes)
}

itPromise("should change text value", async () => {
  let onChange = fn1(ignore)

  <TestableInputSuggestionComboBoxField onChange={onChange->fn} />->render->ignore

  let inputElement = screen->getByRoleExn(#combobox)

  expect(inputElement)->toBeVisible
  expect(inputElement)->toHaveValue("")
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.type_(inputElement, "test")

  expect(onChange)->toHaveBeenCalledTimes(4)
  expect(onChange)->toHaveBeenCalledWith1("test")
  expect(inputElement)->toHaveValue("test")
})

itPromise("should not be possible to interact with it when disabled", async () => {
  let onChange = fn1(ignore)

  <TestableInputSuggestionComboBoxField disabled=true onChange={onChange->fn} />->render->ignore

  let inputElement = screen->getByRoleExn(#combobox)

  await userEvent->TestingLibraryEvent.click(inputElement)

  expectListBoxIsClosed()
  expect(inputElement)->Vitest.not->toHaveFocus
  expect(onChange)->toHaveBeenCalledTimes(0)
})

itPromise("should toggle list upon input or clicking", async () => {
  let {baseElement} = <TestableInputSuggestionComboBoxField />->render

  let inputElement = screen->getByRoleExn(#combobox)
  let inputToggleElement = screen->getByRoleExn(#button)

  expect(inputElement)->toBeVisible
  expect(inputToggleElement)->toBeVisible

  expectListBoxIsClosed()
  expect(inputElement)->Vitest.not->toHaveFocus

  await userEvent->TestingLibraryEvent.click(inputElement)

  expectListBoxIsClosed()
  expect(inputElement)->toHaveFocus

  await userEvent->TestingLibraryEvent.type_(inputElement, "My")

  expectListBoxIsOpened()

  await userEvent->TestingLibraryEvent.click(baseElement)

  expectListBoxIsClosed()
  expect(inputElement)->Vitest.not->toHaveFocus

  await userEvent->TestingLibraryEvent.click(inputToggleElement)

  expectListBoxIsOpened()
  expect(inputElement)->toHaveFocus
})

itPromise("should trigger onFocus/onBlur props", async () => {
  let onChange = fn1(ignore)
  let onFocus = fn0()
  let onBlur = fn0()

  let {baseElement} =
    <TestableInputSuggestionComboBoxField
      onChange={onChange->fn} onFocus={onFocus->fn} onBlur={onBlur->fn}
    />->render

  let inputToggleElement = screen->getByRoleExn(#button)

  expect(inputToggleElement)->toBeVisible
  expectListBoxIsClosed()

  expect(onFocus)->toHaveBeenCalledTimes(0)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(inputToggleElement)

  expectListBoxIsOpened()

  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(baseElement)

  expectListBoxIsClosed()

  expect(onFocus)->toHaveBeenCalledTimes(1)
  expect(onBlur)->toHaveBeenCalledTimes(1)
  expect(onChange)->toHaveBeenCalledTimes(0)
})

itPromise("should interact with listbox options and filter items", async () => {
  let onChange = fn1(ignore)

  let {baseElement} = <TestableInputSuggestionComboBoxField onChange={onChange->fn} />->render

  let inputElement = screen->getByRoleExn(#combobox)
  let inputToggleElement = screen->getByRoleExn(#button)

  expectListBoxIsClosed()
  expect(onChange)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(inputToggleElement)

  expectListBoxIsOpened()
  expectListBoxOptionsToStrictEqual([
    {value: "My first key", selected: false},
    {value: "My second key", selected: false},
  ])
  expect(inputElement)->toHaveValue("")

  await clickListboxOption(0)

  expectFieldChange(
    onChange,
    {
      value: "My first key",
      calledTimes: 1,
    },
  )
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(inputToggleElement)

  expectListBoxIsOpened()
  expectListBoxOptionsToStrictEqual([
    {value: "My first key", selected: true},
    {value: "My second key", selected: false},
  ])

  await clickListboxOption(1)

  expectFieldChange(
    onChange,
    {
      value: "My second key",
      calledTimes: 2,
    },
  )
  expectListBoxIsClosed()

  await userEvent->TestingLibraryEvent.click(baseElement)
  await userEvent->TestingLibraryEvent.type_(inputElement, "{backspace}"->Js.String2.repeat(10))

  expect(inputElement)->toHaveDisplayValue("My ")
  expect(onChange)->toHaveBeenCalledTimes(12)
  expectListBoxIsOpened()
  expectListBoxOptionsToStrictEqual([
    {value: "My first key", selected: false},
    {value: "My second key", selected: true},
  ])

  await userEvent->TestingLibraryEvent.type_(inputElement, "f")

  expect(inputElement)->toHaveDisplayValue("My f")
  expect(onChange)->toHaveBeenCalledTimes(13)
  expectListBoxIsOpened()
  expectListBoxOptionsToStrictEqual([{value: "My first key", selected: false}])

  await userEvent->TestingLibraryEvent.click(baseElement)

  expectListBoxIsClosed()
  expect(inputElement)->Vitest.not->toHaveFocus

  let (clearButtonElement, _inputToggleElement) = screen->getAllByRoleExn2(#button)

  await userEvent->TestingLibraryEvent.click(clearButtonElement)

  expectFieldChange(
    onChange,
    {
      value: "",
      calledTimes: 14,
    },
  )

  expectListBoxIsOpened()
  expect(inputElement)->toHaveFocus
})
