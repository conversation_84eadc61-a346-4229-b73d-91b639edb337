// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`should match snapshot with disable prop set to false 1`] = `
<body>
  <div>
    <div
      class="Touchable__styles.root userSelect-x1hx0egp display-x1lliihq position-x1n2onr6 Touchable__styles.cursorPointer cursor-x1ypdohk"
      data-react-aria-pressable="true"
      role="button"
      tabindex="0"
    >
      <span>
        my button
      </span>
    </div>
  </div>
</body>
`;

exports[`should match snapshot with disable prop set to true 1`] = `
<body>
  <div>
    <div
      aria-disabled="true"
      class="Touchable__styles.root userSelect-x1hx0egp cursor-xt0e3qv display-x1lliihq position-x1n2onr6"
      data-react-aria-pressable="true"
      role="button"
    >
      <span>
        my button
      </span>
    </div>
  </div>
</body>
`;

exports[`should not change displayName 1`] = `"Touchable"`;
