import { StoryFn, Meta } from '@storybook/react'

import {
  ValueIndicator,
  ValueIndicatorProps,
  valueIndicatorIntegerValue,
  valueIndicatorCurrencyValue,
  valueIndicatorPercentValue,
  valueIndicatorDecimalValue,
} from '../../../src/resources/data-visualizations/ValueIndicator'
import { intlCurrencyEur } from '../../../src/primitives/Intl'

const Template: StoryFn<ValueIndicatorProps> = (props: ValueIndicatorProps) => (
  <ValueIndicator {...props} />
)

export default {
  title: 'Resources/Data visualizations/ValueIndicator',
  component: ValueIndicator,
  parameters: { layout: 'centered' },
} as Meta<typeof ValueIndicator>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  value: valueIndicatorIntegerValue(1234),
  variant: 'standard',
}

export const WithCurrencyAmount = Template.bind({})
WithCurrencyAmount.storyName = 'With currency amount'
WithCurrencyAmount.args = {
  value: valueIndicatorCurrencyValue(1239212.093, intlCurrencyEur),
  variant: 'standard',
}

export const WithPercentValue = Template.bind({})
WithPercentValue.storyName = 'With percent value'
WithPercentValue.args = {
  value: valueIndicatorPercentValue(0.93),
  variant: 'standard',
}

export const WithDecimalValue = Template.bind({})
WithDecimalValue.storyName = 'With decimal value'
WithDecimalValue.args = {
  value: valueIndicatorDecimalValue(12.93),
  variant: 'standard',
}
