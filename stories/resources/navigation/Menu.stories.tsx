import { <PERSON>Fn, Meta } from '@storybook/react'

import { Menu, MenuProps } from '../../../src/resources/navigation/Menu'
import { MenuItem } from '../../../src/resources/navigation/MenuItem'

const Template: StoryFn<MenuProps> = (props: MenuProps) => (
  <Menu {...props}>
    <MenuItem
      content={{
        TAG: 'Text',
        _0: 'item 1',
      }}
      action={{
        TAG: 'Callback',
        _0: () => null,
      }}
    />
    <MenuItem
      content={{
        TAG: 'Text',
        _0: 'item 2',
      }}
      action={{
        TAG: 'Callback',
        _0: () => null,
      }}
    />
  </Menu>
)

export default {
  title: 'Resources/Navigation/Menu',
  component: Menu,
} as Meta<typeof Menu>

export const Default = Template.bind({})
Default.storyName = 'Default'

export const Disabled = Template.bind({})
Disabled.storyName = 'When disabled is sets to true'
Disabled.args = { disabled: true }
