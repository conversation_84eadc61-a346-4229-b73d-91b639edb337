import { StoryFn, Meta } from '@storybook/react'

import {
  ResourceDetailsActionsBar,
  ResourceDetailsPage,
  ResourceDetailsPageProps,
  ResourceDetailsNotificationBanner,
  ResourceDetailsNotificationBannerProps,
} from '../../src/core/ResourceDetailsPage'
import { TextStyle } from '../../src/resources/typography/TextStyle'
import { Button } from '../../src/resources/actions/Button'
import { FieldsetLayoutPanel } from '../../src/resources/layout-and-structure/FieldsetLayoutPanel'
import { InputTextField } from '../../src/resources/selection-and-input/InputTextField'

const Template: StoryFn<ResourceDetailsPageProps> = (
  props: ResourceDetailsPageProps,
) => <ResourceDetailsPage {...props} />

export default {
  title: 'Core/ResourceDetailsPage',
  component: ResourceDetailsPage,
} as Meta<typeof ResourceDetailsPage>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  title: 'Resource name',
  children: <TextStyle>Some text</TextStyle>,
}

export const Compact = Template.bind({})
Compact.storyName = 'Compact'
Compact.args = {
  title: 'Resource name',
  variation: 'compact',
  children: <TextStyle>Some text</TextStyle>,
}

function ressurceNotificationBannerValueOk(
  value: string,
): ResourceDetailsNotificationBannerProps['value'] {
  return { TAG: 'Ok', _0: value }
}

export const Complete = Template.bind({})
Complete.storyName = 'Complete'
Complete.args = {
  title: 'Resource name',
  subtitle: 'Resource sub name',
  notificationBanner: (
    <ResourceDetailsNotificationBanner
      value={ressurceNotificationBannerValueOk('Resource notification banner')}
      onRequestClose={() => alert('Close')}
    />
  ),
  actionsBar: (
    <ResourceDetailsActionsBar
      items={[<Button onPress={() => alert('Action')}>Resource action</Button>]}
    />
  ),
  children: (
    <FieldsetLayoutPanel
      title="Fieldset"
      description="The fieldset resource description"
    >
      <InputTextField label="Label" value="" onChange={() => {}} />
    </FieldsetLayoutPanel>
  ),
}
