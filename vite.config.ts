import react from '@vitejs/plugin-react'
import { defineConfig, type UserConfig } from 'vite'
import type { InlineConfig } from 'vitest'
import styleX from 'vite-plugin-stylex'
import path from 'path'

interface VitestConfigExport extends UserConfig {
  test: InlineConfig
}

// https://vitejs.dev/config
export default defineConfig({
  plugins: [
    react({
      jsxRuntime: 'automatic',
      include: '**/*.res.js',
    }),
    styleX(),
  ],
  publicDir: 'public',
  build: {
    outDir: 'build',
    sourcemap: true,
  },
  server: {
    open: true,
    port: 3000,
  },
  resolve: {
    alias: {
      src: path.resolve(__dirname, 'src'),
    },
  },
  test: {
    include: ['tests/**/*__spec.res.js'],
    setupFiles: ['./tests/mocks.cjs', './tests/extends.mjs'],
    globalSetup: './tests/globals.cjs',
    globals: true,
    environment: 'jsdom',
    testTimeout: 10000,
    coverage: {
      reporter: ['text', 'html'],
      exclude: [
        '.storybook/*',
        'build/*',
        'lib/*',
        'node_modules/*',
        'storybook-static/*',
        'stories/*',
        'tests/*',
      ],
    },
    server: {
      deps: {
        fallbackCJS: true, // may improve performances on some systems/runners
      },
    },
    useAtomics: true, // improves performances on latest nodejs versions
    maxConcurrency: 20,
  },
} as VitestConfigExport)
